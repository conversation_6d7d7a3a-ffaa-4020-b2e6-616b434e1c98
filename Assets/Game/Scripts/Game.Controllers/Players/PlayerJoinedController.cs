using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Players;
using Modules.Core;
using Modules.Network;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerJoinedController : ControllerBase
    {
        private PlayersModel playersModel;

        [Inject]
        private void Construct(INetworkClient networkClient, PlayersModel playersModel)
        {
            this.playersModel = playersModel;

            networkClient.OnPlayerJoined.Subscribe(x => HandlePlayerJoined(x.runner, x.player)).AddTo(DisposeCancellationToken);
        }

        public override void Initialize()
        {
            playersModel.CreateOfflineLocalPlayer();
        }

        private void HandlePlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            if (player != runner.LocalPlayer)
            {
                return;
            }

            playersModel.CreateLocalPlayer();
        }
    }
}