using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Extensions
{
    public class TriggerActor<T> : Actor where T : class
    {
        [SerializeField] private LayerMask targetLayer;
        [SerializeField] private string targetTag;

        private readonly List<BoxCollider> colliders = new();
        private readonly ISubject<T> onEntered = new Subject<T>();
        private readonly ISubject<T> onExited = new Subject<T>();

        public IReadOnlyList<BoxCollider> Colliders => colliders;
        public IObservable<T> OnEntered => onEntered;
        public IObservable<T> OnExited => onExited;

        private void Awake()
        {
            GetComponentsInChildren(true, colliders);
        }

        private void OnTriggerEnter(Collider other)
        {
            if (IsValid(other) && other.attachedRigidbody.TryGetComponent(out T component))
            {
                onEntered.OnNext(component);
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (other.attachedRigidbody == null)
            {
                return;
            }

            if (other.attachedRigidbody.TryGetComponent(out T component))
            {
                onExited.OnNext(component);
            }
        }

        public bool IsOverlap(Vector3 position)
        {
            return colliders.Exists(c => c.bounds.Contains(position));
        }

        private bool IsValid(Component other)
        {
            return other.CompareTag(targetTag) && 1 << other.gameObject.layer == targetLayer;
        }
    }
}