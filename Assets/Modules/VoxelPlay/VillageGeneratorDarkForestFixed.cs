using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace VoxelPlay {

	[CreateAssetMenu(menuName = "Voxel Play/Detail Generators/Village Generator", fileName = "VillageGenerator", order = 102)]
	public class VillageGeneratorDarkForestFixed : VoxelPlayDetailGenerator {

        [Range(0,0.1f)]
		public float spawnProbability = 0.02f;
		public ModelDefinition[] buildings;

		[System.Serializable]
		public struct CustomBuildingPlacement {
			public ModelDefinition building;
			public Vector2 position;
		}

		[SerializeField] public CustomBuildingPlacement[] customBuildingPlacements;

		struct BuildingStatus {
			public float height;
			public bool placementStatus;
		}


		VoxelPlayEnvironment env;
		// x,y,z chunk position  w cached terrain height
		Dictionary<Vector3d, BuildingStatus> buildingPositions;

		/// <summary>
		/// Initialization method. Called by Voxel Play at startup.
		/// </summary>
		public override void Init() {
			env = VoxelPlayEnvironment.instance;
			buildingPositions = new Dictionary<Vector3d, BuildingStatus>(100);

			// Fill models with empty blocks so they clear any terrain or vegetation inside them when placing on the world
			if (customBuildingPlacements != null && customBuildingPlacements.Length > 0) {
				for (int k = 0; k < customBuildingPlacements.Length; k++) {
					if (customBuildingPlacements[k].building != null) {
						env.ModelFillInside(customBuildingPlacements[k].building);
					}
				}
			}
		}


		/// <summary>
		/// Called by Voxel Play to inform that player has moved onto another chunk so new detail can start generating
		/// </summary>
		/// <param name="position">Current player position.</param>
		/// <param name="checkOnlyBorders">True means the player has moved to next chunk. False means player position is completely new and all chunks in
		/// range should be checked for detail in this call.</param>
		/// <param name="endTime">Provides a maximum time frame for execution this frame. Compare this with env.stopwatch milliseconds.</param>
		/// <returns><c>true</c>, if there's more work to be executed, <c>false</c> otherwise.</returns>
		public override bool ExploreArea(Vector3d position, bool checkOnlyBorders, long endTime) {
			Debug.Log($"ExploreArea called at position {position}");
			
			if (customBuildingPlacements == null || customBuildingPlacements.Length == 0) {
				Debug.Log($"No custom building placements configured. Array is null: {customBuildingPlacements == null}, Length: {(customBuildingPlacements?.Length ?? 0)}");
				return false;
			}

			Debug.Log($"Checking {customBuildingPlacements.Length} custom building placements");

			// Check each custom building placement to see if it's within range
			for (int i = 0; i < customBuildingPlacements.Length; i++) {
				Vector2 buildingPos2D = customBuildingPlacements[i].position;
				Vector3d buildingPos = new Vector3d(buildingPos2D.x, 0, buildingPos2D.y);
				Vector3d chunkPos = env.GetChunkPosition(buildingPos);

				// Check if this building position is within the exploration range
				var distance = Vector3d.Distance(position, chunkPos);
				int explorationRange = env.visibleChunksDistance + 10;
				float maxDistance = explorationRange * VoxelPlayEnvironment.CHUNK_SIZE;

				Debug.Log($"Building {i}: worldPos={buildingPos2D}, buildingPos3D={buildingPos}, chunkPos={chunkPos}");
				Debug.Log($"Distance check: playerChunk={position}, buildingChunk={chunkPos}, distance={distance}, maxDistance={maxDistance}, explorationRange={explorationRange}");

				if (distance <= maxDistance) {
					Debug.Log($"Building {i} is within range!");
					BuildingStatus bs;
					if (!buildingPositions.TryGetValue(chunkPos, out bs)) {
						float h = env.GetTerrainHeight(buildingPos, false);
						Debug.Log($"Getting terrain height at {buildingPos}: height={h}, waterLevel={env.waterLevel}");
						
						if (h > env.waterLevel) {
							bs.height = h;
							bs.placementStatus = false;

							// No trees on this chunk
							VoxelChunk chunk;
							env.GetChunk(chunkPos, out chunk, false);
							if (chunk != null) {
								chunk.allowTrees = false;
							}
						} else {
							bs.placementStatus = true;
							Debug.Log($"Building {i} is below water level, marking as placed");
						}
						buildingPositions[chunkPos] = bs;
						Debug.Log($"Added building {i} to buildingPositions. Total count: {buildingPositions.Count}");
					} else {
						Debug.Log($"Building {i} already exists in buildingPositions");
					}
				} else {
					Debug.Log($"Building {i} is too far away (distance={distance} > maxDistance={maxDistance})");
				}
			}
			return false;
		}


		/// <summary>
		/// Fills the given chunk with detail. Filled voxels won't be replaced by the terrain generator.
		/// Use Voxel.Empty to fill with void.
		/// </summary>
		/// <param name="chunk">Chunk.</param>
		public override void AddDetail(VoxelChunk chunk) {
			Debug.Log($"AddDetail called for chunk at position {chunk.position}. BuildingPositions count: {buildingPositions.Count}");

			// Check if any building should be placed in this chunk
			if (customBuildingPlacements == null || customBuildingPlacements.Length == 0) return;

			for (int i = 0; i < customBuildingPlacements.Length; i++) {
				Vector2 buildingPos2D = customBuildingPlacements[i].position;
				Vector3d buildingPos = new Vector3d(buildingPos2D.x, 0, buildingPos2D.y);
				Vector3d buildingChunkPos = env.GetChunkPosition(buildingPos);

				Debug.Log($"Checking building {i}: buildingChunkPos={buildingChunkPos}, currentChunk={chunk.position}");

				// Check if this building belongs to the current chunk
				if (buildingChunkPos.Equals(chunk.position)) {
					Debug.Log($"Building {i} belongs to current chunk!");
					
					// Check if we have building status for this position
					BuildingStatus bs;
					if (buildingPositions.TryGetValue(buildingChunkPos, out bs)) {
						Debug.Log($"Found building status: height={bs.height}, placementStatus={bs.placementStatus}");
						
						if (!bs.placementStatus) {
							Debug.Log($"Building not yet placed, proceeding with placement...");
							bs.placementStatus = true;
							buildingPositions[buildingChunkPos] = bs;

							Vector3d pos = new Vector3d(buildingPos2D.x, bs.height, buildingPos2D.y);
							Debug.Log($"Setting building position to {pos}");

							// if this chunk is marked as modified (has been modified by user), don't place any model here
							if (chunk.modified) {
								Debug.Log($"Chunk is modified by user, skipping placement");
								continue;
							}

							ModelDefinition buildingModel = customBuildingPlacements[i].building;
							Debug.Log($"Found building model: {(buildingModel != null ? buildingModel.name : "NULL")}");
							if (buildingModel != null) {
								Debug.Log($"Placing custom building '{buildingModel.name}' at position {pos}");
								env.ModelPlace(pos, buildingModel, WorldRand.Range(0, 3) * 90, 1f, true);
								Debug.Log($"ModelPlace called successfully!");
							} else {
								Debug.Log($"No building model configured for building {i}");
							}
						} else {
							Debug.Log($"Building already placed, skipping");
						}
					} else {
						Debug.Log($"No building status found for building {i} at chunk {buildingChunkPos}");
					}
				}
			}
		}


	}

}
